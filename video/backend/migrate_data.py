#!/usr/bin/env python3
"""
Data migration script for video processing system schema improvements
"""

import sys
import os
import argparse
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.data_migration import DataMigrator
from app.core.database import SessionLocal
from loguru import logger


def main():
    parser = argparse.ArgumentParser(description="Migrate data for video processing system improvements")
    parser.add_argument(
        "--step", 
        choices=["all", "files", "analysis", "audio", "thumbnails", "frames"],
        default="all",
        help="Migration step to run"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be migrated without making changes"
    )
    parser.add_argument(
        "--backup",
        action="store_true", 
        help="Create backup before migration"
    )
    
    args = parser.parse_args()
    
    logger.info("Starting data migration for video processing system")
    logger.info(f"Migration step: {args.step}")
    logger.info(f"Dry run: {args.dry_run}")
    
    if args.backup:
        create_backup()
    
    db = SessionLocal()
    migrator = DataMigrator(db)
    
    try:
        if args.dry_run:
            logger.info("DRY RUN MODE - No changes will be made")
            # TODO: Implement dry run logic
            return
        
        if args.step == "all":
            migrator.migrate_all()
        elif args.step == "files":
            migrator.migrate_file_organization()
        elif args.step == "analysis":
            migrator.migrate_analysis_results_processing_time()
        elif args.step == "audio":
            migrator.populate_missing_audio_file_paths()
        elif args.step == "thumbnails":
            migrator.set_key_frame_thumbnails()
        elif args.step == "frames":
            migrator.migrate_video_frames_to_key_frames()
        
        logger.info("Migration completed successfully!")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)
    finally:
        db.close()


def create_backup():
    """Create backup of database and important files"""
    logger.info("Creating backup...")
    
    import shutil
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path(f"backup_{timestamp}")
    backup_dir.mkdir(exist_ok=True)
    
    # Backup database
    if os.path.exists("app.db"):
        shutil.copy2("app.db", backup_dir / "app.db")
        logger.info(f"Database backed up to {backup_dir}/app.db")
    
    # Backup storage directories
    storage_dirs = ["../storage/uploads", "../storage/processed", "../storage/thumbnails"]
    for storage_dir in storage_dirs:
        if os.path.exists(storage_dir):
            dir_name = Path(storage_dir).name
            shutil.copytree(storage_dir, backup_dir / dir_name, dirs_exist_ok=True)
            logger.info(f"Storage directory {storage_dir} backed up to {backup_dir}/{dir_name}")
    
    logger.info(f"Backup completed in {backup_dir}")


if __name__ == "__main__":
    main()
