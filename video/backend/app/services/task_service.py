"""
任务管理服务
"""

from sqlalchemy.orm import Session
from app.models.task import Task, Video
from app.core.database import get_db
from typing import List, Optional
import os
import uuid
from datetime import datetime


class TaskService:
    """任务管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_task(self, name: str, description: str = "", config: dict = None) -> Task:
        """创建新任务"""
        task = Task(
            name=name,
            description=description,
            config=config or {},
            status="pending",
            progress=0.0
        )
        
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        
        return task
    
    def get_task(self, task_id: int) -> Optional[Task]:
        """获取任务详情"""
        return self.db.query(Task).filter(Task.id == task_id).first()
    
    def get_tasks(self, limit: int = 100, offset: int = 0) -> List[Task]:
        """获取任务列表"""
        return self.db.query(Task).offset(offset).limit(limit).all()
    
    def update_task(self, task_id: int, **updates) -> Optional[Task]:
        """更新任务"""
        task = self.get_task(task_id)
        if not task:
            return None
        
        for key, value in updates.items():
            if hasattr(task, key):
                setattr(task, key, value)
        
        task.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(task)
        
        return task
    
    def delete_task(self, task_id: int) -> bool:
        """删除任务"""
        task = self.get_task(task_id)
        if not task:
            return False
        
        self.db.delete(task)
        self.db.commit()
        
        return True
    
    def update_progress(self, task_id: int, progress: float) -> Optional[Task]:
        """更新任务进度"""
        return self.update_task(task_id, progress=progress)
    
    def set_status(self, task_id: int, status: str) -> Optional[Task]:
        """设置任务状态"""
        return self.update_task(task_id, status=status)
