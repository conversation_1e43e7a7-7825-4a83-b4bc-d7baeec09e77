"""
Data migration utilities for schema and file organization improvements
"""

import os
import shutil
import time
from pathlib import Path
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from loguru import logger
from datetime import datetime

from app.models.task import Video, VideoFrame, AudioTrack, AnalysisResult
from app.utils.file_organization import file_organizer
from app.core.database import SessionLocal


class DataMigrator:
    """Handles migration of existing data to new schema and file organization"""
    
    def __init__(self, db: Session = None):
        self.db = db or SessionLocal()
    
    def migrate_all(self):
        """Run all migration steps"""
        logger.info("Starting comprehensive data migration...")
        
        try:
            # Step 1: Migrate file organization
            self.migrate_file_organization()
            
            # Step 2: Migrate analysis results processing time
            self.migrate_analysis_results_processing_time()
            
            # Step 3: Populate missing audio file paths
            self.populate_missing_audio_file_paths()
            
            # Step 4: Set key frame thumbnails
            self.set_key_frame_thumbnails()
            
            # Step 5: Update video frames to key frames only
            self.migrate_video_frames_to_key_frames()
            
            logger.info("Data migration completed successfully!")
            
        except Exception as e:
            logger.error(f"Data migration failed: {e}")
            raise
    
    def migrate_file_organization(self):
        """Migrate existing files to new organized structure"""
        logger.info("Migrating file organization...")
        
        videos = self.db.query(Video).all()
        
        for video in videos:
            try:
                logger.info(f"Migrating files for video {video.id}")
                
                # Create organized directory structure
                dirs = file_organizer.create_video_directory_structure(video.id)
                
                # Move original video file if needed
                current_video_path = Path(video.file_path)
                new_video_path = file_organizer.get_original_video_path(video.id, video.original_filename)
                
                if current_video_path != new_video_path and current_video_path.exists():
                    shutil.move(str(current_video_path), str(new_video_path))
                    video.file_path = str(new_video_path)
                    logger.info(f"Moved video file to {new_video_path}")
                
                # Migrate audio files
                self._migrate_audio_files(video.id)
                
                # Migrate frame files
                self._migrate_frame_files(video.id)
                
                # Migrate subtitle files (if any exist)
                self._migrate_subtitle_files(video.id)
                
                # Migrate thumbnail files (if any exist)
                self._migrate_thumbnail_files(video.id)
                
            except Exception as e:
                logger.error(f"Failed to migrate files for video {video.id}: {e}")
        
        self.db.commit()
        logger.info("File organization migration completed")
    
    def _migrate_audio_files(self, video_id: int):
        """Migrate audio files for a specific video"""
        audio_tracks = self.db.query(AudioTrack).filter(AudioTrack.video_id == video_id).all()
        
        for track in audio_tracks:
            if track.file_path and os.path.exists(track.file_path):
                # Get new organized path
                codec = Path(track.file_path).suffix[1:]  # Remove dot
                new_path = file_organizer.get_audio_file_path(video_id, track.stream_index, codec)
                
                if Path(track.file_path) != new_path:
                    shutil.move(track.file_path, str(new_path))
                    track.file_path = str(new_path)
                    logger.info(f"Moved audio file to {new_path}")
    
    def _migrate_frame_files(self, video_id: int):
        """Migrate frame files for a specific video"""
        frames = self.db.query(VideoFrame).filter(VideoFrame.video_id == video_id).all()
        
        for frame in frames:
            if frame.file_path and os.path.exists(frame.file_path):
                # Get new organized path
                new_path = file_organizer.get_frame_file_path(video_id, frame.frame_number, is_key_frame=True)
                
                if Path(frame.file_path) != new_path:
                    shutil.move(frame.file_path, str(new_path))
                    frame.file_path = str(new_path)
                    # Mark as key frame since we're only keeping key frames
                    frame.is_key_frame = True
                    logger.info(f"Moved frame file to {new_path}")
    
    def _migrate_subtitle_files(self, video_id: int):
        """Migrate subtitle files for a specific video"""
        # Look for existing subtitle files in old locations
        old_subtitle_patterns = [
            f"uploads/subtitles/video_{video_id}*.srt",
            f"uploads/video_{video_id}/subtitles/*.srt"
        ]
        
        for pattern in old_subtitle_patterns:
            for subtitle_file in Path(".").glob(pattern):
                if subtitle_file.exists():
                    new_path = file_organizer.get_subtitle_file_path(video_id)
                    shutil.move(str(subtitle_file), str(new_path))
                    logger.info(f"Moved subtitle file to {new_path}")
                    break  # Only move the first one found
    
    def _migrate_thumbnail_files(self, video_id: int):
        """Migrate thumbnail files for a specific video"""
        # Look for existing thumbnail files in old locations
        old_thumbnail_patterns = [
            f"uploads/thumbnails/video_{video_id}*.jpg",
            f"uploads/video_{video_id}/thumbnails/*.jpg"
        ]
        
        for pattern in old_thumbnail_patterns:
            for thumbnail_file in Path(".").glob(pattern):
                if thumbnail_file.exists():
                    new_path = file_organizer.get_thumbnail_file_path(video_id)
                    shutil.move(str(thumbnail_file), str(new_path))
                    logger.info(f"Moved thumbnail file to {new_path}")
                    break  # Only move the first one found
    
    def migrate_analysis_results_processing_time(self):
        """Migrate processing_time field from Unix timestamp to duration"""
        logger.info("Migrating analysis results processing time...")
        
        results = self.db.query(AnalysisResult).all()
        
        for result in results:
            if result.processing_time and result.processing_time > 1000000000:  # Likely a Unix timestamp
                # Convert Unix timestamp to duration (assume 10 seconds for old records)
                estimated_duration = 10.0
                
                # Update new fields
                result.processing_duration_seconds = estimated_duration
                result.completed_at = datetime.fromtimestamp(result.processing_time)
                result.started_at = datetime.fromtimestamp(result.processing_time - estimated_duration)
                
                # Keep original for backward compatibility
                logger.info(f"Migrated processing time for analysis result {result.id}")
            
            elif result.processing_time and result.processing_time < 1000:  # Already a duration
                # Just copy to new field
                result.processing_duration_seconds = result.processing_time
                # Set approximate timestamps based on created_at
                if result.created_at:
                    result.completed_at = result.created_at
                    result.started_at = datetime.fromtimestamp(
                        result.created_at.timestamp() - result.processing_time
                    )
        
        self.db.commit()
        logger.info("Analysis results processing time migration completed")
    
    def populate_missing_audio_file_paths(self):
        """Populate missing file_path values for audio tracks"""
        logger.info("Populating missing audio file paths...")
        
        audio_tracks = self.db.query(AudioTrack).filter(AudioTrack.file_path.is_(None)).all()
        
        for track in audio_tracks:
            # Generate expected file path
            codec = "wav"  # Default codec
            expected_path = file_organizer.get_audio_file_path(track.video_id, track.stream_index, codec)
            
            # Check if file exists at expected location
            if expected_path.exists():
                track.file_path = str(expected_path)
                logger.info(f"Set file path for audio track {track.id}: {expected_path}")
            else:
                logger.warning(f"Audio file not found for track {track.id} at {expected_path}")
        
        self.db.commit()
        logger.info("Audio file path population completed")
    
    def set_key_frame_thumbnails(self):
        """Set key frame thumbnails for videos that don't have them"""
        logger.info("Setting key frame thumbnails...")
        
        videos = self.db.query(Video).filter(Video.key_frame_thumbnail_id.is_(None)).all()
        
        for video in videos:
            # Find the first key frame for this video
            first_frame = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video.id,
                VideoFrame.is_key_frame == True
            ).order_by(VideoFrame.timestamp).first()
            
            if first_frame:
                video.key_frame_thumbnail_id = first_frame.id
                logger.info(f"Set key frame thumbnail for video {video.id}: frame {first_frame.id}")
        
        self.db.commit()
        logger.info("Key frame thumbnail setting completed")
    
    def migrate_video_frames_to_key_frames(self):
        """Update video frames to only keep key frames"""
        logger.info("Migrating video frames to key frames only...")
        
        # Mark all existing frames as key frames (since we're keeping them)
        frames = self.db.query(VideoFrame).filter(VideoFrame.is_key_frame.is_(None)).all()
        
        for frame in frames:
            frame.is_key_frame = True
        
        self.db.commit()
        logger.info("Video frames migration to key frames completed")
    
    def cleanup_old_directories(self):
        """Clean up old directory structures after migration"""
        logger.info("Cleaning up old directories...")
        
        old_dirs = [
            "uploads/audio",
            "uploads/frames", 
            "uploads/subtitles",
            "uploads/thumbnails"
        ]
        
        for old_dir in old_dirs:
            if os.path.exists(old_dir):
                try:
                    # Only remove if empty
                    if not os.listdir(old_dir):
                        os.rmdir(old_dir)
                        logger.info(f"Removed empty directory: {old_dir}")
                    else:
                        logger.warning(f"Directory not empty, skipping: {old_dir}")
                except OSError as e:
                    logger.warning(f"Could not remove directory {old_dir}: {e}")


def run_migration():
    """Run the complete data migration"""
    migrator = DataMigrator()
    migrator.migrate_all()
    migrator.cleanup_old_directories()


if __name__ == "__main__":
    run_migration()
