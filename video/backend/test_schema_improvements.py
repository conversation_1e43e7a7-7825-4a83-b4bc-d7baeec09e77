#!/usr/bin/env python3
"""
Test script for database schema and file organization improvements
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.core.database import SessionLocal, engine, Base
from app.models.task import Video, VideoFrame, AudioTrack, AnalysisResult, Task
from app.utils.file_organization import file_organizer
from app.utils.data_migration import DataMigrator
from app.services.video_analysis_service import VideoAnalysisService
from loguru import logger


class SchemaImprovementTester:
    """Test suite for schema improvements"""
    
    def __init__(self):
        self.db = SessionLocal()
        self.test_results = []
    
    def run_all_tests(self):
        """Run all test suites"""
        logger.info("Starting comprehensive schema improvement tests...")
        
        try:
            # Test 1: Database schema validation
            self.test_database_schema()
            
            # Test 2: File organization system
            self.test_file_organization()
            
            # Test 3: Model relationships
            self.test_model_relationships()
            
            # Test 4: Analysis results improvements
            self.test_analysis_results_improvements()
            
            # Test 5: Key frame functionality
            self.test_key_frame_functionality()
            
            # Test 6: Data migration
            self.test_data_migration()
            
            # Test 7: API compatibility
            self.test_api_compatibility()
            
            # Print results
            self.print_test_results()
            
        except Exception as e:
            logger.error(f"Test suite failed: {e}")
            raise
        finally:
            self.db.close()
    
    def test_database_schema(self):
        """Test database schema changes"""
        logger.info("Testing database schema...")
        
        try:
            # Test table creation
            Base.metadata.create_all(bind=engine)
            
            # Test new columns exist
            from sqlalchemy import inspect
            inspector = inspect(engine)
            
            # Check videos table changes
            videos_columns = [col['name'] for col in inspector.get_columns('videos')]
            assert 'key_frame_thumbnail_id' in videos_columns, "key_frame_thumbnail_id column missing"
            
            # Check analysis_results table changes
            analysis_columns = [col['name'] for col in inspector.get_columns('analysis_results')]
            assert 'processing_duration_seconds' in analysis_columns, "processing_duration_seconds column missing"
            assert 'started_at' in analysis_columns, "started_at column missing"
            assert 'completed_at' in analysis_columns, "completed_at column missing"
            
            # Check video_frames table changes
            frames_columns = [col['name'] for col in inspector.get_columns('video_frames')]
            assert 'is_key_frame' in frames_columns, "is_key_frame column missing"
            
            self.test_results.append(("Database Schema", "PASS", "All schema changes applied successfully"))
            
        except Exception as e:
            self.test_results.append(("Database Schema", "FAIL", str(e)))
            raise
    
    def test_file_organization(self):
        """Test file organization system"""
        logger.info("Testing file organization...")
        
        try:
            test_video_id = 999
            
            # Test directory creation
            dirs = file_organizer.create_video_directory_structure(test_video_id)
            
            # Verify all directories exist
            assert dirs['root'].exists(), "Root directory not created"
            assert dirs['audios'].exists(), "Audios directory not created"
            assert dirs['frames'].exists(), "Frames directory not created"
            assert dirs['subtitles'].exists(), "Subtitles directory not created"
            assert dirs['thumbnails'].exists(), "Thumbnails directory not created"
            
            # Test path generation
            audio_path = file_organizer.get_audio_file_path(test_video_id, 0, "wav")
            frame_path = file_organizer.get_frame_file_path(test_video_id, 1, is_key_frame=True)
            subtitle_path = file_organizer.get_subtitle_file_path(test_video_id)
            thumbnail_path = file_organizer.get_thumbnail_file_path(test_video_id)
            
            # Verify paths are in correct structure
            assert f"videos/{test_video_id}/audios" in str(audio_path), "Audio path incorrect"
            assert f"videos/{test_video_id}/frames" in str(frame_path), "Frame path incorrect"
            assert f"videos/{test_video_id}/subtitles" in str(subtitle_path), "Subtitle path incorrect"
            assert f"videos/{test_video_id}/thumbnails" in str(thumbnail_path), "Thumbnail path incorrect"
            
            # Cleanup test directories
            import shutil
            shutil.rmtree(dirs['root'], ignore_errors=True)
            
            self.test_results.append(("File Organization", "PASS", "File organization system working correctly"))
            
        except Exception as e:
            self.test_results.append(("File Organization", "FAIL", str(e)))
            raise
    
    def test_model_relationships(self):
        """Test model relationships"""
        logger.info("Testing model relationships...")
        
        try:
            # Create test data
            task = Task(name="Test Task", status="pending")
            self.db.add(task)
            self.db.flush()
            
            video = Video(
                task_id=task.id,
                filename="test.mp4",
                original_filename="test.mp4",
                file_path="/tmp/test.mp4"
            )
            self.db.add(video)
            self.db.flush()
            
            # Create test frame
            frame = VideoFrame(
                video_id=video.id,
                frame_number=1,
                timestamp=0.0,
                file_path="/tmp/frame.jpg",
                is_key_frame=True
            )
            self.db.add(frame)
            self.db.flush()
            
            # Test key frame thumbnail relationship
            video.key_frame_thumbnail_id = frame.id
            self.db.commit()
            
            # Verify relationship
            self.db.refresh(video)
            assert video.key_frame_thumbnail is not None, "Key frame thumbnail relationship not working"
            assert video.key_frame_thumbnail.id == frame.id, "Key frame thumbnail ID mismatch"
            
            # Cleanup
            self.db.delete(frame)
            self.db.delete(video)
            self.db.delete(task)
            self.db.commit()
            
            self.test_results.append(("Model Relationships", "PASS", "All relationships working correctly"))
            
        except Exception as e:
            self.test_results.append(("Model Relationships", "FAIL", str(e)))
            raise
    
    def test_analysis_results_improvements(self):
        """Test analysis results improvements"""
        logger.info("Testing analysis results improvements...")
        
        try:
            from datetime import datetime
            
            # Create test data
            task = Task(name="Test Task", status="pending")
            self.db.add(task)
            self.db.flush()
            
            video = Video(
                task_id=task.id,
                filename="test.mp4",
                original_filename="test.mp4",
                file_path="/tmp/test.mp4"
            )
            self.db.add(video)
            self.db.flush()
            
            # Create analysis result with new fields
            result = AnalysisResult(
                video_id=video.id,
                step="basic_analysis",
                result={"test": "data"},
                confidence=0.95,
                processing_time=10.0,  # Backward compatibility
                processing_duration_seconds=10.0,
                started_at=datetime.now(),
                completed_at=datetime.now()
            )
            self.db.add(result)
            self.db.commit()
            
            # Verify new fields
            self.db.refresh(result)
            assert result.processing_duration_seconds == 10.0, "Processing duration not set"
            assert result.started_at is not None, "Started at not set"
            assert result.completed_at is not None, "Completed at not set"
            
            # Cleanup
            self.db.delete(result)
            self.db.delete(video)
            self.db.delete(task)
            self.db.commit()
            
            self.test_results.append(("Analysis Results", "PASS", "Analysis results improvements working"))
            
        except Exception as e:
            self.test_results.append(("Analysis Results", "FAIL", str(e)))
            raise
    
    def test_key_frame_functionality(self):
        """Test key frame functionality"""
        logger.info("Testing key frame functionality...")
        
        try:
            # Create test video frame
            task = Task(name="Test Task", status="pending")
            self.db.add(task)
            self.db.flush()
            
            video = Video(
                task_id=task.id,
                filename="test.mp4",
                original_filename="test.mp4",
                file_path="/tmp/test.mp4"
            )
            self.db.add(video)
            self.db.flush()
            
            # Create key frame
            key_frame = VideoFrame(
                video_id=video.id,
                frame_number=1,
                timestamp=0.0,
                file_path="/tmp/keyframe.jpg",
                width=1920,
                height=1080,
                is_key_frame=True
            )
            self.db.add(key_frame)
            self.db.commit()
            
            # Test key frame query
            key_frames = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video.id,
                VideoFrame.is_key_frame == True
            ).all()
            
            assert len(key_frames) == 1, "Key frame query failed"
            assert key_frames[0].is_key_frame == True, "Key frame flag not set"
            
            # Cleanup
            self.db.delete(key_frame)
            self.db.delete(video)
            self.db.delete(task)
            self.db.commit()
            
            self.test_results.append(("Key Frame Functionality", "PASS", "Key frame functionality working"))
            
        except Exception as e:
            self.test_results.append(("Key Frame Functionality", "FAIL", str(e)))
            raise
    
    def test_data_migration(self):
        """Test data migration functionality"""
        logger.info("Testing data migration...")
        
        try:
            migrator = DataMigrator(self.db)
            
            # Test migration methods exist and are callable
            assert hasattr(migrator, 'migrate_file_organization'), "migrate_file_organization method missing"
            assert hasattr(migrator, 'migrate_analysis_results_processing_time'), "migrate_analysis_results_processing_time method missing"
            assert hasattr(migrator, 'populate_missing_audio_file_paths'), "populate_missing_audio_file_paths method missing"
            assert hasattr(migrator, 'set_key_frame_thumbnails'), "set_key_frame_thumbnails method missing"
            
            self.test_results.append(("Data Migration", "PASS", "Data migration functionality available"))
            
        except Exception as e:
            self.test_results.append(("Data Migration", "FAIL", str(e)))
            raise
    
    def test_api_compatibility(self):
        """Test API compatibility"""
        logger.info("Testing API compatibility...")
        
        try:
            # Test that API modules can be imported
            from app.api.v1.endpoints.videos import router as videos_router
            from app.api.v1.endpoints.analysis import router as analysis_router
            
            # Verify routers exist
            assert videos_router is not None, "Videos router not available"
            assert analysis_router is not None, "Analysis router not available"
            
            self.test_results.append(("API Compatibility", "PASS", "API endpoints compatible"))
            
        except Exception as e:
            self.test_results.append(("API Compatibility", "FAIL", str(e)))
            raise
    
    def print_test_results(self):
        """Print test results summary"""
        logger.info("\n" + "="*60)
        logger.info("SCHEMA IMPROVEMENT TEST RESULTS")
        logger.info("="*60)
        
        passed = 0
        failed = 0
        
        for test_name, status, message in self.test_results:
            status_symbol = "✓" if status == "PASS" else "✗"
            logger.info(f"{status_symbol} {test_name}: {status}")
            if message:
                logger.info(f"  {message}")
            
            if status == "PASS":
                passed += 1
            else:
                failed += 1
        
        logger.info("="*60)
        logger.info(f"SUMMARY: {passed} passed, {failed} failed")
        logger.info("="*60)
        
        if failed > 0:
            sys.exit(1)


def main():
    """Run the test suite"""
    tester = SchemaImprovementTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
