# Frontend Schema Improvements Testing Guide

This document provides a comprehensive testing guide for all frontend changes made to accommodate the database schema and file organization improvements.

## Overview of Changes

### 1. API Response Updates
- **Analysis Results**: Now include `processing_duration_seconds`, `started_at`, `completed_at` fields
- **Frame Extraction**: Changed from `/extract-frames` to `/extract-key-frames` endpoint
- **File Paths**: Updated to use organized structure `/static/videos/{video_id}/`
- **Thumbnails**: Now use `key_frame_thumbnail_id` instead of `thumbnail_path`

### 2. Updated Components
- `src/stores/videos.js` - Enhanced API methods and error handling
- `src/views/VideoAnalysis/AnalysisWorkspace.vue` - Updated file paths and thumbnails
- `src/views/VideoAnalysis/AnalysisDetail.vue` - Added processing time display and extraction buttons
- `src/views/TaskManagement/TaskDetail.vue` - Updated thumbnail handling
- `src/utils/filePathUtils.js` - New utility for file path management
- `src/utils/backwardCompatibility.js` - Backward compatibility layer

## Testing Checklist

### 1. Video Store API Methods ✅

**Test extractKeyFrames method:**
```javascript
// In browser console
const videoStore = useVideoStore()
await videoStore.extractKeyFrames(1)
// Should return: { key_frames_only: true, frame_count: N, frame_files: [...] }
```

**Test extractAudio method:**
```javascript
await videoStore.extractAudio(1)
// Should return success response with organized file paths
```

**Test getAnalysisResults with new fields:**
```javascript
const results = await videoStore.getAnalysisResults(1)
console.log(results[0])
// Should include: processing_duration, started_at, completed_at
```

### 2. File Path Handling ✅

**Test video URL generation:**
```javascript
import { getVideoUrl } from '@/utils/filePathUtils'
const video = { id: 1, filename: 'test.mp4' }
const url = getVideoUrl(video)
// Should return: http://localhost:8000/static/videos/1/test.mp4
```

**Test thumbnail URL generation:**
```javascript
import { getVideoThumbnail } from '@/utils/filePathUtils'
const video = { id: 1, key_frame_thumbnail_id: 5 }
const thumbnailUrl = getVideoThumbnail(video)
// Should return: http://localhost:8000/static/videos/1/frames/keyframe_000005.jpg
```

### 3. Analysis Results Display ✅

**Navigate to Analysis Detail page:**
1. Go to `/analysis/{video_id}`
2. Check for "分析性能统计" section
3. Verify processing time display shows:
   - 处理时长 (formatted duration)
   - 开始时间 (formatted timestamp)
   - 完成时间 (formatted timestamp)
   - 置信度 (percentage)

### 4. Thumbnail Display ✅

**Check thumbnail display in various components:**
1. **AnalysisWorkspace**: Video list should show thumbnails using new system
2. **TaskDetail**: Video cards should show thumbnails using new system
3. **Verify fallback**: Components should gracefully handle missing thumbnails

### 5. Frame Extraction UI ✅

**Test new extraction buttons in AnalysisDetail:**
1. Navigate to `/analysis/{video_id}`
2. Click "提取关键帧" button
3. Verify loading state and success message
4. Click "提取音频" button
5. Verify loading state and success message

### 6. Error Handling ✅

**Test error scenarios:**
```javascript
// Test with non-existent video
await videoStore.extractKeyFrames(99999)
// Should show user-friendly error: "视频 99999 不存在"

// Test network error
// Disconnect network and try any API call
// Should show: "网络连接失败，请检查网络设置"
```

### 7. Backward Compatibility ✅

**Test normalization functions:**
```javascript
import { normalizeVideo, normalizeAnalysisResult } from '@/utils/backwardCompatibility'

// Test old format video
const oldVideo = { id: 1, thumbnail_path: 'old/path.jpg' }
const normalized = normalizeVideo(oldVideo)
// Should include normalized thumbnail URL

// Test old format analysis result
const oldResult = { processing_time: 10.5 }
const normalizedResult = normalizeAnalysisResult(oldResult)
// Should include processing_duration field
```

## Manual Testing Steps

### Step 1: Video List Display
1. Navigate to `/analysis`
2. Verify video list loads correctly
3. Check that thumbnails display properly
4. Verify video selection works

### Step 2: Video Analysis Detail
1. Select a video and click "查看详情"
2. Verify analysis performance statistics display
3. Test "提取关键帧" button functionality
4. Test "提取音频" button functionality
5. Check that all tabs (基础信息, 内容要素, 剧情分析) work

### Step 3: Task Management
1. Navigate to `/tasks`
2. Select a task with videos
3. Verify video thumbnails display in task detail
4. Check that video information is complete

### Step 4: File Path Validation
1. Open browser developer tools
2. Check Network tab for API requests
3. Verify file URLs use new organized structure:
   - Videos: `/static/videos/{id}/{filename}`
   - Thumbnails: `/static/videos/{id}/frames/keyframe_*.jpg`
   - Audio: `/static/videos/{id}/audios/audio_track_*.wav`

### Step 5: Error Handling
1. Try accessing non-existent video: `/analysis/99999`
2. Verify graceful error handling
3. Test network disconnection scenarios
4. Check that error messages are user-friendly

## Expected Results

### ✅ Successful Tests Should Show:
- Video thumbnails display correctly using key frames
- Processing time information shows in analysis detail
- File paths use new organized structure
- API calls use updated endpoints (`/extract-key-frames`)
- Error messages are user-friendly and localized
- Backward compatibility works for old data formats

### ❌ Issues to Watch For:
- Broken thumbnail images (404 errors)
- Missing processing time information
- API calls to old endpoints (`/extract-frames`)
- Unhandled errors or generic error messages
- File path inconsistencies

## Performance Validation

### Check Network Requests:
1. Open browser DevTools → Network tab
2. Navigate through the application
3. Verify API requests use correct endpoints:
   - ✅ `POST /videos/{id}/extract-key-frames`
   - ✅ `POST /videos/{id}/extract-audio`
   - ✅ `GET /analysis/{id}` returns new fields

### Check Console for Errors:
1. Open browser DevTools → Console tab
2. Navigate through the application
3. Verify no JavaScript errors
4. Check for deprecation warnings

## Rollback Plan

If issues are found:

1. **Immediate**: Revert file path utilities to use old structure
2. **API**: Ensure backend maintains old endpoint compatibility
3. **Data**: Use backward compatibility layer for all API responses
4. **UI**: Hide new features (processing time display, extraction buttons) if needed

## Success Criteria

All tests pass when:
- ✅ All video thumbnails display correctly
- ✅ Processing time information shows properly formatted
- ✅ File extraction buttons work without errors
- ✅ Error handling provides clear user feedback
- ✅ No console errors or network failures
- ✅ Backward compatibility maintains old functionality
- ✅ Performance is maintained or improved

## Next Steps

After successful testing:
1. Deploy to staging environment
2. Run full integration tests
3. Monitor for any issues in production
4. Plan removal of deprecated code after stable period
