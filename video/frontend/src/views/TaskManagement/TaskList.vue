<template>
  <div class="min-h-screen bg-gray-50" style="background-color: #f9fafb; min-height: 100vh;">
    <!-- 页面头部 -->
    <div class="bg-white shadow" style="background-color: white; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center">
          <h1 class="text-3xl font-bold text-gray-900">任务管理</h1>
          <button
            @click="showCreateModal = true"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            新建任务
          </button>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p class="mt-4 text-gray-500">加载中...</p>
        </div>

        <!-- 空状态 -->
        <div v-else-if="tasks.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">暂无任务</h3>
          <p class="mt-1 text-sm text-gray-500">开始创建您的第一个视频分析任务</p>
        </div>

        <!-- 任务网格 -->
        <div v-else class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <div
            v-for="task in tasks"
            :key="task.id"
            class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer"
            @click="viewTask(task.id)"
          >
            <div class="p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 truncate">{{ task.name }}</h3>
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(task.status)"
                >
                  {{ getStatusText(task.status) }}
                </span>
              </div>
              
              <p class="mt-2 text-sm text-gray-500 line-clamp-2">
                {{ task.description || '暂无描述' }}
              </p>
              
              <div class="mt-4">
                <div class="flex justify-between text-sm text-gray-500 mb-1">
                  <span>进度</span>
                  <span>{{ Math.round(task.progress || 0) }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-primary-600 h-2 rounded-full transition-all"
                    :style="{ width: `${task.progress || 0}%` }"
                  ></div>
                </div>
              </div>
              
              <div class="mt-4 flex justify-between items-center text-sm text-gray-500">
                <span>{{ task.video_count || 0 }} 个视频</span>
                <div class="flex items-center space-x-2">
                  <span>{{ formatDate(task.created_at) }}</span>
                  <button
                    v-if="task.status === 'failed'"
                    @click.stop="retryTask(task.id)"
                    class="text-red-600 hover:text-red-800 text-xs font-medium"
                  >
                    重试
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建任务模态框 -->
    <div
      v-if="showCreateModal"
      class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
      @click="showCreateModal = false"
    >
      <div
        class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"
        @click.stop
      >
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">创建新任务</h3>
          
          <form @submit.prevent="createTask">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">任务名称</label>
              <input
                v-model="newTask.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="请输入任务名称"
              />
            </div>
            
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">任务描述</label>
              <textarea
                v-model="newTask.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="请输入任务描述（可选）"
              ></textarea>
            </div>
            
            <div class="flex justify-end space-x-3">
              <button
                type="button"
                @click="showCreateModal = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="!newTask.name.trim()"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"
              >
                创建
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useTaskStore } from '@/stores/tasks'
import { format } from 'date-fns'

const router = useRouter()
const appStore = useAppStore()
const taskStore = useTaskStore()

// 响应式数据
const loading = ref(false)
const tasks = ref([])
const showCreateModal = ref(false)
const newTask = ref({
  name: '',
  description: ''
})

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    tasks.value = await taskStore.fetchTasks()
  } catch (error) {
    appStore.showError('加载失败', '无法加载任务列表')
  } finally {
    loading.value = false
  }
}

const createTask = async () => {
  try {
    await taskStore.createTask(newTask.value)
    showCreateModal.value = false
    newTask.value = { name: '', description: '' }
    appStore.showSuccess('创建成功', '任务已创建')
    await loadTasks()
  } catch (error) {
    appStore.showError('创建失败', '无法创建任务')
  }
}

const viewTask = (taskId) => {
  router.push(`/tasks/${taskId}`)
}

const retryTask = async (taskId) => {
  try {
    // 获取任务详情，找到关联的视频
    const task = await taskStore.fetchTask(taskId)
    if (task.videos && task.videos.length > 0) {
      // 重试第一个失败的视频分析
      const failedVideo = task.videos.find(v => v.status === 'failed')
      if (failedVideo) {
        await videoStore.retryAnalysis(failedVideo.id)
        appStore.showSuccess('重试成功', '分析任务已重新启动')
        await loadTasks() // 重新加载任务列表
      }
    }
  } catch (error) {
    appStore.showError('重试失败', '无法重新启动分析任务')
  }
}

const getStatusClass = (status) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    failed: 'bg-red-100 text-red-800',
    paused: 'bg-orange-100 text-orange-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    failed: '失败',
    paused: '已暂停'
  }
  return texts[status] || '未知'
}

const formatDate = (dateString) => {
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm')
}

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>
